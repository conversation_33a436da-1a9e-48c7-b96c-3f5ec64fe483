/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Interfaces and implementations that are internal to OpenTelemetry.
 *
 * <p>All the content under this package and its subpackages are considered not part of the public
 * API, and must not be used by users of the OpenTelemetry library.
 */
@ParametersAreNonnullByDefault
package io.opentelemetry.api.trace.propagation.internal;

import javax.annotation.ParametersAreNonnullByDefault;
