name: Prepare patch release
on:
  workflow_dispatch:

permissions:
  contents: read

jobs:
  prepare-patch-release:
    permissions:
      contents: write  # for git push to PR branch
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - run: |
          if [[ ! $GITHUB_REF_NAME =~ ^release/v[0-9]+\.[0-9]+\.x$ ]]; then
            echo this workflow should only be run against release branches
            exit 1
          fi

          if ! grep --quiet "^## Unreleased$" CHANGELOG.md; then
            echo the change log is missing an \"Unreleased\" section
            exit 1
          fi

      - name: Set environment variables
        run: |
          version=$(.github/scripts/get-version.sh)
          if [[ $version =~ ^([0-9]+\.[0-9]+)\.([0-9]+)$ ]]; then
            major_minor="${BASH_REMATCH[1]}"
            patch="${BASH_REMATCH[2]}"
          else
            echo "unexpected version: $version"
            exit 1
          fi
          echo "VERSION=$major_minor.$((patch + 1))" >> $GITHUB_ENV

      - name: Update version
        run: .github/scripts/update-version.sh $VERSION

      - name: Update the change log with the approximate release date
        run: |
          date=$(date "+%Y-%m-%d")
          sed -Ei "s/^## Unreleased$/## Version $VERSION ($date)/" CHANGELOG.md

      - name: Use CLA approved github bot
        run: .github/scripts/use-cla-approved-github-bot.sh

      - uses: actions/create-github-app-token@df432ceedc7162793a195dd1713ff69aefc7379e # v2.0.6
        id: otelbot-token
        with:
          app-id: ${{ vars.OTELBOT_APP_ID }}
          private-key: ${{ secrets.OTELBOT_PRIVATE_KEY }}

      - name: Create pull request
        env:
          # not using secrets.GITHUB_TOKEN since pull requests from that token do not run workflows
          GH_TOKEN: ${{ steps.otelbot-token.outputs.token }}
        run: |
          message="Prepare release $VERSION"
          branch="otelbot/prepare-release-${VERSION}"

          git checkout -b $branch
          git commit -a -m "$message"
          git push --set-upstream origin $branch
          gh pr create --title "[$GITHUB_REF_NAME] $message" \
                       --body "$message." \
                       --base $GITHUB_REF_NAME
