name: Benchmark Tags

on:
  workflow_dispatch:

permissions:
  contents: read

jobs:
  sdk-benchmark:
    permissions:
      contents: write # for git push to benchmarks branch
    name: Benchmark SDK
    runs-on: equinix-bare-metal
    timeout-minutes: 10
    strategy:
      fail-fast: false
      matrix:
        tag-version:
          - v1.6.0
          - v1.7.0
          - v1.7.1
          - v1.10.0
          - v1.10.1
          - v1.11.0
          - v1.12.0
          - v1.13.0
          - v1.14.0
          - v1.15.0
          - v1.16.0
          - v1.17.0
          - v1.18.0
          - v1.19.0
          - v1.21.0
          - v1.22.0
          - v1.23.0
          - v1.23.1
          - v1.24.0
          - v1.25.0
          - v1.26.0
          - v1.27.0
          - v1.28.0
          - v1.29.0
          - v1.30.0
          - v1.30.1
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ matrix.tag-version }}

      - id: setup-java
        name: Set up Java for build
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          distribution: temurin
          java-version: 17

      - name: Set up gradle
        uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1
      - name: Run jmh
        run: ./gradlew jmhJar

      - name: Run Benchmark
        run: |
          cd sdk/trace/build
          java -jar libs/opentelemetry-sdk-trace-*-jmh.jar -rf json SpanBenchmark SpanPipelineBenchmark ExporterBenchmark

      - name: Store benchmark results
        uses: benchmark-action/github-action-benchmark@d48d326b4ca9ba73ca0cd0d59f108f9e02a381c7 # v1.20.4
        with:
          tool: 'jmh'
          output-file-path: sdk/trace/build/jmh-result.json
          gh-pages-branch: benchmarks
          github-token: ${{ secrets.GITHUB_TOKEN }}
          benchmark-data-dir-path: "benchmarks"
          auto-push: true
          ref: ${{ matrix.tag-version }}
