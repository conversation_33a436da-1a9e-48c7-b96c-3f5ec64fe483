name: Release
on:
  workflow_dispatch:

permissions:
  contents: read

jobs:
  release:
    permissions:
      contents: write # for creating the release
    runs-on: ubuntu-24.04
    outputs:
      version: ${{ steps.create-github-release.outputs.version }}
      prior-version: ${{ steps.create-github-release.outputs.prior-version }}
    steps:
      - run: |
          if [[ $GITHUB_REF_NAME != release/* ]]; then
            echo this workflow should only be run against release branches
            exit 1
          fi

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          distribution: temurin
          java-version: 17

      - name: Set up gradle
        uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1

      - name: Build and publish artifacts
        run: ./gradlew assemble publishToSonatype closeAndReleaseSonatypeStagingRepository
        env:
          SONATYPE_USER: ${{ secrets.SONATYPE_USER }}
          SONATYPE_KEY: ${{ secrets.SONATYPE_KEY }}
          GPG_PRIVATE_KEY: ${{ secrets.GPG_PRIVATE_KEY }}
          GPG_PASSWORD: ${{ secrets.GPG_PASSWORD }}

      - name: Set environment variables
        run: |
          version=$(.github/scripts/get-version.sh)
          if [[ $version =~ ^([0-9]+)\.([0-9]+)\.([0-9]+) ]]; then
            major="${BASH_REMATCH[1]}"
            minor="${BASH_REMATCH[2]}"
            patch="${BASH_REMATCH[3]}"
          else
            echo "unexpected version: $version"
            exit 1
          fi
          if [[ $patch == 0 ]]; then
            if [[ $minor == 0 ]]; then
              prior_major=$((major - 1))
              prior_minor=$(grep -Po "^## Version $prior_major.\K[0-9]+" CHANGELOG.md | head -1)
              prior_version="$prior_major.$prior_minor"
            else
              prior_version="$major.$((minor - 1)).0"
            fi
          else
            prior_version="$major.$minor.$((patch - 1))"
          fi
          echo "VERSION=$version" >> $GITHUB_ENV
          echo "PRIOR_VERSION=$prior_version" >> $GITHUB_ENV

        # check out main branch to verify there won't be problems with merging the change log
        # at the end of this workflow
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: main

      - name: Check that change log update was merged to main
        run: |
          if [[ $VERSION == *.0 ]]; then
            # not making a patch release
            if ! grep --quiet "^## Version $VERSION " CHANGELOG.md; then
              echo the pull request generated by prepare-release-branch.yml needs to be merged first
              exit 1
            fi
          fi

        # back to the release branch
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # tags are needed for the generate-release-contributors.sh script
          fetch-depth: 0

      - name: Generate release notes
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # conditional blocks not indented because of the heredoc
          if [[ $VERSION != *.0 ]]; then
          cat > /tmp/release-notes.txt << EOF
          This is a patch release on the previous $PRIOR_VERSION release, fixing the issue(s) below.

          EOF
          fi

          # CHANGELOG_SECTION.md is also used at the end of the release workflow
          # for copying the change log updates to main
          sed -n "0,/^## Version $VERSION /d;/^## Version /q;p" CHANGELOG.md \
            > /tmp/CHANGELOG_SECTION.md

          # the complex perl regex is needed because markdown docs render newlines as soft wraps
          # while release notes render them as line breaks
          perl -0pe 's/(?<!\n)\n *(?!\n)(?![-*] )(?![1-9]+\. )/ /g' /tmp/CHANGELOG_SECTION.md \
            >> /tmp/release-notes.txt

          # conditional block not indented because of the heredoc
          if [[ $VERSION == *.0 ]]; then
          cat >> /tmp/release-notes.txt << EOF

          ### 🙇 Thank you
          This release was possible thanks to the following contributors who shared their brilliant ideas and awesome pull requests:

          EOF

          .github/scripts/generate-release-contributors.sh v$PRIOR_VERSION >> /tmp/release-notes.txt
          fi

      - id: create-github-release
        name: Create GitHub release
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh release create --target $GITHUB_REF_NAME \
                            --title "Version $VERSION" \
                            --notes-file /tmp/release-notes.txt \
                            v$VERSION

          # these are used as job outputs
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "prior-version=$PRIOR_VERSION" >> $GITHUB_OUTPUT

  update-apidiff-baseline-and-docs-to-released-version:
    permissions:
      contents: write # for git push to PR branch
    runs-on: ubuntu-latest
    needs:
      - release
    steps:
      # add change log sync (if any) into this PR since the apidiff update
      # is required before any other PR can be merged anyway
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Copy change log section from release branch
        env:
          VERSION: ${{ needs.release.outputs.version }}
        run: |
          sed -n "0,/^## Version $VERSION /d;/^## Version /q;p" CHANGELOG.md \
            > /tmp/changelog-section.md

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: main

      - name: Merge change log to main
        env:
          VERSION: ${{ needs.release.outputs.version }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          release_date=$(gh release view v$VERSION --json publishedAt --jq .publishedAt | sed 's/T.*//')
          RELEASE_DATE=$release_date .github/scripts/merge-change-log-after-release.sh
          git add CHANGELOG.md

      - name: Wait for release to be available in maven central
        env:
          VERSION: ${{ needs.release.outputs.version }}
        run: |
          until curl --silent \
                     --show-error \
                     --output /dev/null \
                     --head \
                     --fail \
                     https://repo1.maven.org/maven2/io/opentelemetry/opentelemetry-api/$VERSION/opentelemetry-api-$VERSION.jar
          do
            sleep 60
          done

      - name: Update apidiff baseline
        env:
          VERSION: ${{ needs.release.outputs.version }}
          PRIOR_VERSION: ${{ needs.release.outputs.prior-version }}
        run: |
          ./gradlew japicmp -PapiBaseVersion=$PRIOR_VERSION -PapiNewVersion=$VERSION
          ./gradlew --refresh-dependencies japicmp
          git add docs/apidiffs

      - name: Update versions in README.md
        env:
          VERSION: ${{ needs.release.outputs.version }}
        run: |
          ./gradlew updateVersionInDocs -Prelease.version=$VERSION
          git add README.md

      - name: Use CLA approved bot
        run: .github/scripts/use-cla-approved-github-bot.sh

      - uses: actions/create-github-app-token@df432ceedc7162793a195dd1713ff69aefc7379e # v2.0.6
        id: otelbot-token
        with:
          app-id: ${{ vars.OTELBOT_APP_ID }}
          private-key: ${{ secrets.OTELBOT_PRIVATE_KEY }}

      - name: Create pull request against main
        env:
          VERSION: ${{ needs.release.outputs.version }}
          # not using secrets.GITHUB_TOKEN since pull requests from that token do not run workflows
          GH_TOKEN: ${{ steps.otelbot-token.outputs.token }}
        run: |
          message="Update apidiff baseline and documentation versions to released version $VERSION"
          body="Update apidiff baseline and documentation versions to released version \`$VERSION\`."
          branch="otelbot/update-apidiff-baseline-and-documentation-to-released-version-${VERSION}"

          git checkout -b $branch
          git commit -m "$message"
          git push --set-upstream origin $branch
          gh pr create --title "$message" \
                       --body "$body" \
                       --base main
