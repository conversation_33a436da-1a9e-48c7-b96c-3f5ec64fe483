name: Prepare release branch
on:
  workflow_dispatch:

permissions:
  contents: read

jobs:
  prereqs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Verify prerequisites
        run: |
          if [[ $GITHUB_REF_NAME != main ]]; then
            echo this workflow should only be run against main
            exit 1
          fi

          if ! grep --quiet "^## Unreleased$" CHANGELOG.md; then
            echo the change log is missing an \"Unreleased\" section
            exit 1
          fi

  create-pull-request-against-release-branch:
    permissions:
      contents: write  # for git push to PR branch
    runs-on: ubuntu-latest
    needs:
      - prereqs
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Create release branch
        run: |
          version=$(.github/scripts/get-version.sh)
          version=${version//-SNAPSHOT/}
          if [[ $version =~ ^([0-9]+)\.([0-9]+)\.0$ ]]; then
            release_branch_name=$(echo $version | sed -E 's/([0-9]+)\.([0-9]+)\.0/release\/v\1.\2.x/')
          else
            echo "unexpected version: $version"
            exit 1
          fi

          git push origin HEAD:$release_branch_name

          echo "VERSION=$version" >> $GITHUB_ENV
          echo "RELEASE_BRANCH_NAME=$release_branch_name" >> $GITHUB_ENV

      - name: Update version
        run: sed -Ei "s/val snapshot = true/val snapshot = false/" version.gradle.kts

      - name: Update the change log with the approximate release date
        run: |
          date=$(date "+%Y-%m-%d")
          sed -Ei "s/^## Unreleased$/## Version $VERSION ($date)/" CHANGELOG.md

      - name: Use CLA approved github bot
        run: .github/scripts/use-cla-approved-github-bot.sh

      - uses: actions/create-github-app-token@df432ceedc7162793a195dd1713ff69aefc7379e # v2.0.6
        id: otelbot-token
        with:
          app-id: ${{ vars.OTELBOT_APP_ID }}
          private-key: ${{ secrets.OTELBOT_PRIVATE_KEY }}

      - name: Create pull request against the release branch
        env:
          # not using secrets.GITHUB_TOKEN since pull requests from that token do not run workflows
          GH_TOKEN: ${{ steps.otelbot-token.outputs.token }}
        run: |
          message="Prepare release $VERSION"
          branch="otelbot/prepare-release-${VERSION}"

          git checkout -b $branch
          git commit -a -m "$message"
          git push --set-upstream origin $branch
          gh pr create --title "[$RELEASE_BRANCH_NAME] $message" \
                       --body "$message." \
                       --base $RELEASE_BRANCH_NAME

  create-pull-request-against-main:
    permissions:
      contents: write  # for git push to PR branch
    runs-on: ubuntu-latest
    needs:
      - prereqs
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set environment variables
        run: |
          version=$(.github/scripts/get-version.sh)
          if [[ $version =~ ^([0-9]+)\.([0-9]+)\.0$ ]]; then
            major="${BASH_REMATCH[1]}"
            minor="${BASH_REMATCH[2]}"
            next_version="$major.$((minor + 1)).0"
          else
            echo "unexpected version: $version"
            exit 1
          fi
          echo "NEXT_VERSION=${next_version}" >> $GITHUB_ENV
          echo "VERSION=$version" >> $GITHUB_ENV

      - name: Update version
        run: .github/scripts/update-version.sh $NEXT_VERSION

      - name: Update the change log on main
        run: |
          # the actual release date on main will be updated at the end of the release workflow
          date=$(date "+%Y-%m-%d")
          sed -Ei "s/^## Unreleased$/## Unreleased\n\n## Version $VERSION ($date)/" CHANGELOG.md

      - name: Use CLA approved github bot
        run: .github/scripts/use-cla-approved-github-bot.sh

      - uses: actions/create-github-app-token@df432ceedc7162793a195dd1713ff69aefc7379e # v2.0.6
        id: otelbot-token
        with:
          app-id: ${{ vars.OTELBOT_APP_ID }}
          private-key: ${{ secrets.OTELBOT_PRIVATE_KEY }}

      - name: Create pull request against main
        env:
          # not using secrets.GITHUB_TOKEN since pull requests from that token do not run workflows
          GH_TOKEN: ${{ steps.otelbot-token.outputs.token }}
        run: |
          message="Update version to $NEXT_VERSION"
          body="Update version to \`$NEXT_VERSION\`."
          branch="otelbot/update-version-to-${NEXT_VERSION}"

          git checkout -b $branch
          git commit -a -m "$message"
          git push --set-upstream origin $branch
          gh pr create --title "$message" \
                       --body "$body" \
                       --base main
