name: Build

on:
  push:
    branches:
      - main
      - release/*
  pull_request:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  build:
    name: Build
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os:
          - macos-latest
          - macos-13
          - ubuntu-latest
          - windows-latest
        test-java-version:
          - 8
          - 11
          - 17
          - 21
          - 23
        # Collect coverage on latest LTS
        include:
          - os: ubuntu-latest
            test-java-version: 21
            coverage: true
            jmh-based-tests: true
        # macos-latest drops support for java 8 temurin. Run java 8 on macos-13. Run java 11, 17, 21 on macos-latest.
        exclude:
          - os: macos-latest
            test-java-version: 8
          - os: macos-13
            test-java-version: 11
          - os: macos-13
            test-java-version: 17
          - os: macos-13
            test-java-version: 21
          - os: macos-13
            test-java-version: 23
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - id: setup-java-test
        name: Set up Java ${{ matrix.test-java-version }} for tests
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          distribution: temurin
          java-version: ${{ matrix.test-java-version }}

      - id: setup-java
        name: Set up Java for build
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          distribution: temurin
          java-version: 17

      - name: Set up gradle
        uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1
      - name: Build
        run: >
            ./gradlew build
            ${{ matrix.coverage && 'jacocoTestReport' || '' }}
            -PtestJavaVersion=${{ matrix.test-java-version }}
            "-Porg.gradle.java.installations.paths=${{ steps.setup-java-test.outputs.path }}"
            "-Porg.gradle.java.installations.auto-download=false"
        env:
          # JMH-based tests run only  if this environment variable is set to true
          RUN_JMH_BASED_TESTS: ${{ matrix.jmh-based-tests }}

      - name: Check for diff
        # The jApiCmp diff compares current to latest, which isn't appropriate for release branches
        # this fails on windows because of the bash-specific if/then/else syntax, but that's ok
        # because we only need to run this validation once (on any platform)
        if: ${{ matrix.os != 'windows-latest' && !startsWith(github.ref_name, 'release/') && !startsWith(github.base_ref, 'release/') }}
        run: |
          # need to "git add" in case any generated files did not already exist
          git add docs/apidiffs
          if git diff --cached --quiet
          then 
            echo "No diff detected."
          else 
            echo "Diff detected - did you run './gradlew jApiCmp'?"
            echo $(git diff --cached --name-only)
            echo $(git diff --cached)
            exit 1
          fi

      - uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24 # v5.4.3
        if: ${{ matrix.coverage }}
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

      - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        if: ${{ matrix.coverage }}
        with:
          name: coverage-report
          path: all/build/reports/jacoco/test/html

  markdown-link-check:
    # release branches are excluded to avoid unnecessary maintenance
    if: ${{ !startsWith(github.ref_name, 'release/') }}
    uses: ./.github/workflows/reusable-markdown-link-check.yml

  misspell-check:
    # release branches are excluded to avoid unnecessary maintenance
    if: ${{ !startsWith(github.ref_name, 'release/') }}
    uses: ./.github/workflows/reusable-misspell-check.yml

  publish-snapshots:
    # the condition is on the steps below instead of here on the job, because skipping the job
    # causes the job to show up as canceled in the GitHub UI which prevents the build section from
    # collapsing when everything (else) is green
    #
    # and the name is updated when the steps below are skipped which makes what's happening clearer
    # in the GitHub UI
    #
    # note: the condition below has to be written so that '' is last since it resolves to false
    # and so would not short-circuit if used in the second-last position
    name: publish-snapshots${{ (github.ref_name != 'main' || github.repository != 'open-telemetry/opentelemetry-java') && ' (skipped)' || '' }}
    # intentionally not blocking snapshot publishing on markdown-link-check or misspell-check
    needs: build
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - id: setup-java
        name: Set up Java
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          distribution: temurin
          java-version: 17

      - name: Set up gradle
        uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1
        # skipping release branches because the versions in those branches are not snapshots
        # (also this skips pull requests)
        if: ${{ github.ref_name == 'main' && github.repository == 'open-telemetry/opentelemetry-java' }}
      - name: Publish to Sonatype
        run: ./gradlew assemble publishToSonatype
        # skipping release branches because the versions in those branches are not snapshots
        # (also this skips pull requests)
        if: ${{ github.ref_name == 'main' && github.repository == 'open-telemetry/opentelemetry-java' }}
        env:
          SONATYPE_USER: ${{ secrets.SONATYPE_USER }}
          SONATYPE_KEY: ${{ secrets.SONATYPE_KEY }}
          GPG_PRIVATE_KEY: ${{ secrets.GPG_PRIVATE_KEY }}
          GPG_PASSWORD: ${{ secrets.GPG_PASSWORD }}

  build-graal:
    name: Build GraalVM
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        test-graal-version:
          - 21
          - 23
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: graalvm/setup-graalvm@e1df20a713a4cc6ab5b0eb03f0e0dcdc0199b805 # v1.3.4
        with:
          java-version: ${{ matrix.test-graal-version }}
          distribution: 'graalvm'
          components: 'native-image'
          github-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Running test
        run: |
          echo "GRAALVM_HOME: $GRAALVM_HOME"
          echo "JAVA_HOME: $JAVA_HOME"
          java --version
          native-image --version
          ./gradlew nativeTest

  required-status-check:
    # markdown-link-check is not required so pull requests are not blocked if external links break
    # misspell-check is not required so pull requests are not blocked if the misspell dictionary is
    # updated
    needs:
      - build
      - build-graal
    runs-on: ubuntu-latest
    if: always()
    steps:
      - if: |
          needs.build.result != 'success' ||
          needs.build-graal.result != 'success'
        run: exit 1
