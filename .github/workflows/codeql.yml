name: CodeQL

on:
  pull_request:
    branches:
      - main
      - release/*
      - benchmarks
  push:
    branches:
      - main
      - release/*
      - benchmarks
  schedule:
    - cron: "23 16 * * 2"  # weekly at 16:23 UTC on Tuesday

permissions:
  contents: read

jobs:
  analyze:
    permissions:
      contents: read
      actions: read  # for github/codeql-action/init to get workflow details
      security-events: write  # for github/codeql-action/analyze to upload SARIF results
    strategy:
      fail-fast: false
      matrix:
        include:
          - language: actions
          - language: java
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Java 17
        if: matrix.language == 'java'
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          distribution: temurin
          java-version: 17

      - name: Set up gradle
        if: matrix.language == 'java'
        uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1

      - name: Initialize CodeQL
        uses: github/codeql-action/init@181d5eefc20863364f96762470ba6f862bdef56b # v3.29.2
        with:
          languages: ${{ matrix.language }}
          # using "latest" helps to keep up with the latest Kotlin support
          # see https://github.com/github/codeql-action/issues/1555#issuecomment-1452228433
          tools: latest

      - name: Assemble
        if: matrix.language == 'java'
        # --no-build-cache is required for codeql to analyze all modules
        # --no-daemon is required for codeql to observe the compilation
        # (see https://docs.github.com/en/code-security/codeql-cli/getting-started-with-the-codeql-cli/preparing-your-code-for-codeql-analysis#specifying-build-commands)
        run: ./gradlew assemble --no-build-cache --no-daemon

      - name: Perform CodeQL analysis
        uses: github/codeql-action/analyze@181d5eefc20863364f96762470ba6f862bdef56b # v3.29.2
        with:
          category: "/language:${{matrix.language}}"