name: Build tracecontext testsuite

on:
  push:
    paths:
      - 'integration-tests/tracecontext/docker/**'
      - '.github/workflows/build-tracecontext-testsuite.yaml'
    branches:
      - main
  workflow_dispatch:

permissions:
  contents: read

jobs:
  publish:
    permissions:
      contents: read
      packages: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Login to GitHub package registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          context: integration-tests/tracecontext/docker
          push: true
          tags: ghcr.io/open-telemetry/java-test-containers:w3c-tracecontext-testsuite
