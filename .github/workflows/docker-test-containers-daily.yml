name: Copy test container docker images (daily)

on:
  schedule:
    - cron: "23 3 * * *"
  workflow_dispatch:

permissions:
  contents: read

jobs:
  copy-images:
    permissions:
      contents: read
      packages: write
    strategy:
      matrix:
        include:
          - source: jaegertracing/all-in-one:1.32
            target_image: jaeger:1.32
          - source: otel/opentelemetry-collector-contrib:latest
            target_image: otel-collector
          - source: shopify/toxiproxy:latest
            target_image: toxiproxy
          - source: eclipse-temurin:17-jre-focal
            target_image: openjdk17
    runs-on: ubuntu-latest
    steps:
      - name: Docker login
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Copy image
        # Non-debug image doesn't seem to support reading the .docker creds.
        run: |
          docker run --rm -v $HOME/.docker:/root/.docker gcr.io/go-containerregistry/crane:debug \
          cp ${{ matrix.source }} ghcr.io/open-telemetry/opentelemetry-java/${{ matrix.target_image }}
