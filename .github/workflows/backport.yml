name: Backport
on:
  workflow_dispatch:
    inputs:
      number:
        description: "The pull request # to backport"
        required: true

permissions:
  contents: read

jobs:
  backport:
    permissions:
      contents: write  # for git push to PR branch
    runs-on: ubuntu-latest
    steps:
      - run: |
          if [[ ! $GITHUB_REF_NAME =~ ^release/v[0-9]+\.[0-9]+\.x$ ]]; then
            echo this workflow should only be run against release branches
            exit 1
          fi

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # history is needed to run git cherry-pick below
          fetch-depth: 0

      - name: Use CLA approved github bot
        run: .github/scripts/use-cla-approved-github-bot.sh

      - uses: actions/create-github-app-token@df432ceedc7162793a195dd1713ff69aefc7379e # v2.0.6
        id: otelbot-token
        with:
          app-id: ${{ vars.OTELBOT_APP_ID }}
          private-key: ${{ secrets.OTELBOT_PRIVATE_KEY }}

      - name: Create pull request
        env:
          NUMBER: ${{ github.event.inputs.number }}
          # not using secrets.GITHUB_TOKEN since pull requests from that token do not run workflows
          GH_TOKEN: ${{ steps.otelbot-token.outputs.token }}
        run: |
          commit=$(gh pr view $NUMBER --json mergeCommit --jq .mergeCommit.oid)
          title=$(gh pr view $NUMBER --json title --jq .title)

          branch="otelbot/backport-${NUMBER}-to-${GITHUB_REF_NAME//\//-}"

          git checkout -b $branch
          git cherry-pick $commit
          git push --set-upstream origin $branch
          gh pr create --title "[$GITHUB_REF_NAME] $title" \
                       --body "Clean cherry-pick of #$NUMBER to the \`$GITHUB_REF_NAME\` branch." \
                       --base $GITHUB_REF_NAME
