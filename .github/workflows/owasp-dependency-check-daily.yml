# the benefit of this over renovate is that this also analyzes transitive dependencies
# while renovate (at least currently) only analyzes top-level dependencies
name: OWASP dependency check (daily)

on:
  schedule:
    - cron: "30 1 * * *" # daily at 1:30 UTC
  workflow_dispatch:

permissions:
  contents: read

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          distribution: temurin
          java-version: 17

      - name: Set up gradle
        uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1

      - name: Check dependencies
        run: ./gradlew dependencyCheckAnalyze
        env:
          NVD_API_KEY: ${{ secrets.NVD_API_KEY }}

      - name: Upload report
        if: always()
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          path: javaagent/build/reports

  workflow-notification:
    permissions:
      contents: read
      issues: write
    needs:
      - analyze
    if: always()
    uses: ./.github/workflows/reusable-workflow-notification.yml
    with:
      success: ${{ needs.analyze.result == 'success' }}
