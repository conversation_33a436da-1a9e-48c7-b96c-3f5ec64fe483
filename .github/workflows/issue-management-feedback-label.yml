name: Issue management - remove needs feedback label

on:
  issue_comment:
    types: [created]

permissions:
  contents: read

jobs:
  issue_comment:
    permissions:
      contents: read
      issues: write
      pull-requests: write
    if: >
      contains(github.event.issue.labels.*.name, 'needs author feedback') &&
      github.event.comment.user.login == github.event.issue.user.login
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Remove label
        env:
          ISSUE_NUMBER: ${{ github.event.issue.number }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh issue edit --remove-label "needs author feedback" $ISSUE_NUMBER
