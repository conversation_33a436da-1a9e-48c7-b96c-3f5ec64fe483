name: Javadoc.io site crawler (daily)

on:
  schedule:
    - cron: "30 1 * * *" # daily at 1:30 UTC
  workflow_dispatch:

permissions:
  contents: read

jobs:
  crawl:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          distribution: temurin
          java-version: 17

      - name: Set up gradle
        uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1

      - name: Run crawler
        run: ./gradlew :javadoc-crawler:crawl
