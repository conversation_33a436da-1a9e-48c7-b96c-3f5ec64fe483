name: Reusable - Misspell check

on:
  workflow_call:

permissions:
  contents: read

jobs:
  misspell-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Install misspell
        run: |
          curl -L -o install-misspell.sh https://raw.githubusercontent.com/client9/misspell/master/install-misspell.sh
          sh ./install-misspell.sh

      - name: Run misspell
        run: bin/misspell -error .
