name: OSSF Scorecard

on:
  push:
    branches:
      - main
  schedule:
    - cron: "43 6 * * 5" # weekly at 06:43 (UTC) on Friday
  workflow_dispatch:

permissions: read-all

jobs:
  analysis:
    runs-on: ubuntu-latest
    permissions:
      # Needed for Code scanning upload
      security-events: write
      # Needed for GitHub OIDC token if publish_results is true
      id-token: write
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - uses: ossf/scorecard-action@05b42c624433fc40578a4040d5cf5e36ddca8cde # v2.4.2
        with:
          results_file: results.sarif
          results_format: sarif
          publish_results: true

      # Upload the results as artifacts (optional). Commenting out will disable
      # uploads of run results in SARIF format to the repository Actions tab.
      # https://docs.github.com/en/actions/advanced-guides/storing-workflow-data-as-artifacts
      - name: "Upload artifact"
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: SARIF file
          path: results.sarif
          retention-days: 5

      # Upload the results to GitHub's code scanning dashboard (optional).
      # Commenting out will disable upload of results to your repo's Code Scanning dashboard
      - name: "Upload to code-scanning"
        uses: github/codeql-action/upload-sarif@181d5eefc20863364f96762470ba6f862bdef56b # v3.29.2
        with:
          sarif_file: results.sarif
