name: Reusable - Markdown link check

on:
  workflow_call:

permissions:
  contents: read

jobs:
  markdown-link-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: lycheeverse/lychee-action@82202e5e9c2f4ef1a55a3d02563e1cb6041e5332 # v2.4.1
        with:
          # excluding links to pull requests and issues is done for performance
          args: >
            --include-fragments
            --exclude "^https://github.com/open-telemetry/opentelemetry-java/(issues|pull)/\\d+$"
            --max-retries 6
            --retry-wait-time 10
            --max-concurrency 1
            .
