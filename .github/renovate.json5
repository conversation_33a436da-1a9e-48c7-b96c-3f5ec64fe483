{
  $schema: 'https://docs.renovatebot.com/renovate-schema.json',
  extends: [
    'config:best-practices',
    'helpers:pinGitHubActionDigestsToSemver',
  ],
  packageRules: [
    {
      // this is to reduce the number of renovate PRs
      matchManagers: [
        'github-actions',
        'dockerfile',
      ],
      extends: [
        'schedule:weekly',
      ],
      groupName: 'weekly update',
    },
    {
      matchPackageNames: [
        'io.opentelemetry.contrib:opentelemetry-aws-xray-propagator',
        'io.opentelemetry.proto:opentelemetry-proto',
        'io.opentelemetry.semconv:opentelemetry-semconv-incubating',
      ],
      // Renovate's default behavior is only to update from unstable -> unstable if it's for the
      // major.minor.patch, under the assumption that you would want to update to the stable version
      // of that release instead of the unstable version for a future release
      // (TODO remove once the artifacts above release stable versions)
      ignoreUnstable: false,
      allowedVersions: '!/\\-SNAPSHOT$/',
    },
    {
      // junit-pioneer 2+ requires Java 11+
      matchPackageNames: [
        'org.junit-pioneer:junit-pioneer',
      ],
      matchUpdateTypes: [
        'major',
      ],
      enabled: false,
    },
    {
      // mockito 5+ requires Java 11+
      matchUpdateTypes: [
        'major',
      ],
      enabled: false,
      matchPackageNames: [
        'org.mockito:{/,}**',
      ],
    },
    {
      // jqf-fuzz version 1.8+ requires Java 11+
      matchPackageNames: [
        'edu.berkeley.cs.jqf:jqf-fuzz',
      ],
      matchUpdateTypes: [
        'major',
        'minor',
      ],
      enabled: false,
    },
    {
      // pinned version for compatibility
      matchPackageNames: [
        'org.jetbrains.kotlinx:kotlinx-coroutines-core',
      ],
      matchCurrentVersion: '1.5.2',
      enabled: false,
    },
    {
      groupName: 'spotless packages',
      matchPackageNames: [
        'com.diffplug.spotless{/,}**',
      ],
    },
    {
      // equals verifier v4+ requires java 17+
      groupName: 'nl.jqno.equalsverifier',
      matchPackageNames: [ 'equalsverifier'],
      matchUpdateTypes: [ 'major' ],
      enabled: false
    }
  ],
}
