---
name: Bug report
about: Create a report to help us improve
title: ''
labels: Bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**Steps to reproduce**
If possible, provide a recipe for reproducing the error.

**What did you expect to see?**
A clear and concise description of what you expected to see.

**What did you see instead?**
A clear and concise description of what you saw instead.

**What version and what artifacts are you using?**
Artifacts: (e.g., `opentelemetry-api`, `opentelemetry-sdk`, which exporters, etc)
Version: (e.g., `v0.4.0`, `1eb551b`, etc)
How did you reference these artifacts? (excerpt from your `build.gradle`, `pom.xml`, etc)

**Environment**
Compiler: (e.g., "Temurin 17.0.7")
OS: (e.g., "Ubuntu 20.04")
Runtime (if different from JDK above): (e.g., "Oracle JRE 8u251")
OS (if different from OS compiled on): (e.g., "Windows Server 2019")

**Additional context**
Add any other context about the problem here.
